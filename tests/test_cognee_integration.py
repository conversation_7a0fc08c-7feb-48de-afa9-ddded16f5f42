"""
Integration tests for <PERSON>gne<PERSON> tool in Nerve Agent.
"""

import os
import sys
import pytest
import aiohttp
from unittest.mock import AsyncMock, patch, MagicMock, PropertyMock
from python.tools.cognee_tool import <PERSON>gneeTool, CogneeConfig, SearchType
import asyncio
import json
from collections import namedtuple
from yarl import URL
from multidict import CIMultiDict, CIMultiDictProxy
from aiohttp import hdrs
from contextlib import asynccontextmanager

# Enable asyncio mode for pytest
pytest_plugins = ('pytest_asyncio',)

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Test data
TEST_DOCUMENT = """
Artificial intelligence (AI) is intelligence demonstrated by machines, as opposed to 
intelligence of humans or other animals. Example tasks in which this is done include 
reasoning, learning, decision-making, perception, and natural language understanding.
"""

# Fixtures
@pytest.fixture
def test_config():
    """Fixture for test configuration."""
    return CogneeConfig(
        service_url="http://localhost:8000",
        api_key="test-api-key",
        default_dataset="test_dataset"
    )

class AsyncContextManager:
    """Helper class to mock async context managers."""
    def __init__(self, mock_obj):
        self.mock_obj = mock_obj
        
    async def __aenter__(self):
        return self.mock_obj
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass

def create_mock_request_info(url=None, method='GET', headers=None):
    """Create a mock aiohttp.RequestInfo object for testing.
    
    Args:
        url: The request URL (str or URL object)
        method: The HTTP method (default: 'GET')
        headers: Request headers (dict or CIMultiDict)
    
    Returns:
        A namedtuple with the same attributes as aiohttp.RequestInfo
    """
    # Handle URL
    if url is None:
        url = URL('http://localhost:8000/api/v1/test')
    elif isinstance(url, str):
        url = URL(url)
    
    # Handle headers
    if headers is None:
        headers = CIMultiDictProxy(CIMultiDict({'Content-Type': 'application/json'}))
    else:
        if not isinstance(headers, (CIMultiDictProxy, CIMultiDict)):
            headers = CIMultiDict(headers)
        if isinstance(headers, CIMultiDict):
            if 'Content-Type' not in headers:
                headers['Content-Type'] = 'application/json'
            headers = CIMultiDictProxy(headers)
    
    # Create a simple namedtuple with the required attributes
    MockRequestInfo = namedtuple('MockRequestInfo', ['method', 'url', 'headers', 'real_url'])
    return MockRequestInfo(
        method=method,
        url=url,
        headers=headers,
        real_url=url
    )


class MockResponse:
    """Mock response object for aiohttp with proper async context manager support."""
    def __init__(self, status=200, json_data=None, text_data=None, headers=None, url=None):
        self.status = status
        self._json_data = json_data or {}
        self._text_data = text_data or '{}'
        self._headers = headers or {'Content-Type': 'application/json'}
        self._closed = False
        self._url = url or "http://localhost:8000/api/v1/test"
        
    @property
    def headers(self):
        return self._headers
        
    async def json(self):
        if isinstance(self._json_data, Exception):
            raise self._json_data
        return self._json_data
        
    async def text(self):
        return self._text_data
        
    async def __aenter__(self):
        if self._closed:
            raise RuntimeError("Cannot enter closed response")
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
        
    async def close(self):
        self._closed = True
        
    def raise_for_status(self):
        if 400 <= self.status < 600:
            http_error_msg = f"HTTP {self.status}"
            if self._text_data:
                try:
                    error_data = json.loads(self._text_data)
                    if 'detail' in error_data:
                        http_error_msg = f"{http_error_msg}: {error_data['detail']}"
                except json.JSONDecodeError:
                    http_error_msg = f"{http_error_msg}: {self._text_data}"
            
            # Create a proper ClientResponseError with a RequestInfo instance
            request_info = create_mock_request_info()
            
            # Use the proper constructor for ClientResponseError
            error = aiohttp.ClientResponseError(
                request_info=request_info,
                history=(),
                status=self.status,
                message=http_error_msg
            )
            raise error

@pytest.fixture
def mock_session():
    """Fixture that provides a mock aiohttp ClientSession with proper async context manager support."""
    # Create a mock session
    mock_session = AsyncMock(spec=aiohttp.ClientSession)
    
    # Create a class to track request state for retry testing
    class RequestState:
        def __init__(self):
            self.count = 0
            self._lock = asyncio.Lock()
        
        async def increment(self):
            async with self._lock:
                self.count += 1
                return self.count
            
        def reset(self):
            self.count = 0
    
    request_state = RequestState()
    
    # Add a reset_counter method to the mock session
    def reset_counter():
        request_state.reset()
    
    mock_session.reset_counter = reset_counter
    
    # Create a mock request method that returns an async context manager
    @asynccontextmanager
    async def mock_request(method, url, **kwargs):
        nonlocal request_state
        request_count = await request_state.increment()
        
        # Default response
        json_data = {"status": "success"}
        status_code = 200
        
        # Check for specific test cases
        if "/api/v1/search" in str(url):
            query = str(kwargs.get('params', {}).get('query', ''))
            
            # For retry test - first request fails, subsequent requests succeed
            if "test query" in query:
                if request_count == 1:
                    error_text = '{"detail":"Bad Gateway"}'
                    response = MockResponse(
                        status=502,
                        text_data=error_text,
                        headers={'Content-Type': 'application/json'}
                    )
                    try:
                        yield response
                    finally:
                        await response.__aexit__(None, None, None)
                    return
                else:
                    json_data = {"results": ["result1", "result2"]}
            # For error handling test - always return 500
            elif "test_error_handling" in query:
                error_text = '{"detail":"Internal server error"}'
                response = MockResponse(
                    status=500,
                    text_data=error_text,
                    headers={'Content-Type': 'application/json'}
                )
                try:
                    yield response
                finally:
                    await response.__aexit__(None, None, None)
                return
            # For successful search test
            else:
                json_data = {"results": ["result1", "result2"]}
                
        # For document upload test - return the document_id directly
        elif "/api/v1/documents" in str(url) and method == "POST":
            json_data = {"document_id": "doc123"}
            status_code = 200
            
        # For cognify test
        elif "/api/v1/cognify" in str(url) and method == "POST":
            json_data = {
                "processed": True,
                "entities": ["artificial intelligence", "machine learning"]
            }
        
        # Create the response with the appropriate data
        response = MockResponse(
            status=status_code,
            json_data=json_data,
            text_data=json.dumps(json_data),
            headers={'Content-Type': 'application/json'}
        )
        try:
            yield response
        finally:
            await response.__aexit__(None, None, None)
    
    # Create a wrapper class to hold our mock request function and state
    class MockRequestWrapper:
        def __init__(self, request_func, state):
            self._request_func = request_func
            self._state = state
            
        def __call__(self, *args, **kwargs):
            return self._request_func(*args, **kwargs)
            
        def reset_counter(self):
            self._state.reset()
    
    # Create the wrapper instance
    mock_request_wrapper = MockRequestWrapper(mock_request, request_state)
    
    # Replace the mock request with our wrapper
    mock_request = mock_request_wrapper
    
    # Set up the mock session to use our mock request method
    mock_session.request = mock_request
    
    # Set up the session to be used as an async context manager
    async def mock_aenter():
        return mock_session
        
    async def mock_aexit(exc_type, exc_val, exc_tb):
        pass
    
    mock_session.__aenter__ = mock_aenter
    mock_session.__aexit__ = mock_aexit
    
    return mock_session

import pytest_asyncio

@pytest_asyncio.fixture
async def cognee_tool(test_config, mock_session):
    """Fixture for CogneeTool with mocked session."""
    # Create a mock for the session property that returns our mock session
    async def mock_session_property(self):
        return mock_session
    
    # Create a property mock that will be used to replace the session property
    mock_prop = property(mock_session_property)
    
    # Patch the session property at the class level
    with patch('python.tools.cognee_tool.CogneeTool.session', mock_prop):
        # Create the tool with our test config
        tool = CogneeTool(test_config)
        # Also set the internal _session for direct access
        tool._session = mock_session
        yield tool

# Tests
class TestCogneeTool:
    """Test cases for CogneeTool."""

    @pytest.mark.asyncio
    async def test_add_document_success(self, cognee_tool, mock_session):
        """Test successful document addition."""
        # Configure the mock to return the expected response
        async def mock_request(*args, **kwargs):
            return MockResponse(200, json_data={"status": "success"})
            
        # Set the side effect for the mock request
        mock_session.request.side_effect = mock_request
        
        # Call the method
        result = await cognee_tool.add_document("test content", "test.txt")

        # The CogneeTool returns the response as-is
        assert result == {"status": "success"}

    @pytest.mark.asyncio
    async def test_search_success(self, cognee_tool, mock_session):
        """Test successful search."""
        # Reset the request counter before starting the test
        mock_session.reset_counter()
        
        # Call the method with a query that will trigger success response
        result = await cognee_tool.search("successful search")

        # Assert the result is correct
        assert result == {"results": ["result1", "result2"]}

    @pytest.mark.asyncio
    async def test_cognify_success(self, cognee_tool, mock_session):
        """Test successful cognify operation."""
        # Configure the mock response
        json_data = {
            "processed": True,
            "entities": ["artificial intelligence", "machine learning"]
        }
        
        # Override the post method for this test
        async def mock_post(url, **kwargs):
            return MockResponse(200, json_data=json_data)
            
        mock_session.post = mock_post

        # Call the method
        result = await cognee_tool.cognify("test_doc")

        # Assert the result is correct
        assert result == json_data

    @pytest.mark.asyncio
    async def test_error_handling(self, cognee_tool, mock_session):
        """Test error handling in API requests."""
        # Reset the request counter before starting the test
        mock_session.reset_counter()
        
        # Call the method and expect an exception
        with pytest.raises(Exception) as exc_info:
            await cognee_tool.search("test_error_handling")

        # Assert the error message is correct
        assert "500, message='HTTP 500: Internal server error'" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_retry_mechanism(self, test_config):
        """Test retry mechanism on transient failures."""
        # Track the number of calls
        call_count = 0
        
        # Create a mock response for the successful case
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {"results": ["result1", "result2"]}
        
        # Create a mock context manager for the successful response
        mock_context_manager = AsyncMock()
        mock_context_manager.__aenter__.return_value = mock_response
        
        # Create a mock session with AsyncMock
        mock_session = AsyncMock()
        
        # Configure the mock session's request method
        async def mock_request(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            
            if call_count == 1:
                print("First call: Simulating 502 Bad Gateway")
                # First call fails with 502
                headers = CIMultiDict({"Content-Type": "application/json"})
                headers_proxy = CIMultiDictProxy(headers)
                request_info = aiohttp.RequestInfo(
                    url=URL("http://localhost:8000/api/v1/search"),
                    method="GET",
                    headers=headers_proxy,
                    real_url=URL("http://localhost:8000/api/v1/search")
                )
                error = aiohttp.ClientResponseError(
                    request_info=request_info,
                    history=(),
                    status=502,
                    message='Bad Gateway',
                    headers=headers_proxy
                )
                raise error
            else:
                # Subsequent calls succeed
                print(f"Call {call_count}: Returning successful response")
                return mock_context_manager
        
        # Set up the mock session's request method
        mock_session.request.side_effect = mock_request
        
        # Create a mock ClientSession class that returns our mock session
        class MockClientSession:
            def __init__(self, *args, **kwargs):
                pass
                
            async def __aenter__(self):
                return mock_session
                
            async def __aexit__(self, *args):
                pass
        
        # Create a new tool
        tool = CogneeTool(test_config)
        
        # Patch the aiohttp.ClientSession class to return our mock session
        with patch('aiohttp.ClientSession', new=MockClientSession):
            # Call the method (should retry and succeed)
            print("Calling tool.search()...")
            result = await tool.search("test query")
            print(f"Search result: {result}")
            
            # Verify we got the expected successful response
            assert result == {"results": ["result1", "result2"]}, f"Unexpected response: {result}"
            
            # Verify the request was retried (should have been called twice: once failing, once succeeding)
            assert call_count == 2, f"Expected 2 calls, but got {call_count}"
            
            # Verify the mock was called with the expected arguments
            mock_session.request.assert_called()
            print("Test completed successfully")

# Run tests with: pytest tests/test_cognee_integration.py -v

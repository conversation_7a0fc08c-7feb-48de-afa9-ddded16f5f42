.themeToggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background: var(--gray-alpha-100);
  color: var(--foreground);
  cursor: pointer;
  transition: all 0.2s ease;
}

.themeToggle:hover {
  background: var(--gray-alpha-200);
}

@media (max-width: 600px) {
  .themeToggle {
    width: 32px;
    height: 32px;
  }
}
.page {
  --gray-rgb: 0, 0, 0;
  --gray-alpha-200: rgba(var(--gray-rgb), 0.08);
  --gray-alpha-100: rgba(var(--gray-rgb), 0.05);

  --button-primary-hover: #383838;
  --button-secondary-hover: #f2f2f2;

  display: grid;
  grid-template-rows: auto 1fr auto;
  min-height: 100svh;
  padding: 0;
  gap: 0;
  font-family: var(--font-geist-sans);
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) .page {
    --gray-rgb: 255, 255, 255;
    --gray-alpha-200: rgba(var(--gray-rgb), 0.145);
    --gray-alpha-100: rgba(var(--gray-rgb), 0.06);

    --button-primary-hover: #ccc;
    --button-secondary-hover: #1a1a1a;
  }
}

html[data-theme="dark"] .page {
  --gray-rgb: 255, 255, 255;
  --gray-alpha-200: rgba(var(--gray-rgb), 0.145);
  --gray-alpha-100: rgba(var(--gray-rgb), 0.06);

  --button-primary-hover: #ccc;
  --button-secondary-hover: #1a1a1a;
}

.main {
  display: flex;
  flex-direction: column;
  gap: 32px;
  max-width: 800px;
  width: 100%;
  margin: 60px auto 20px;
  padding: 0 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 15%;
  width: 100%;
}

.headerActions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.headerButton {
  appearance: none;
  border-radius: 6px;
  height: 36px;
  padding: 0 12px;
  border: 1px solid var(--gray-alpha-200);
  background: transparent;
  transition: background 0.2s, color 0.2s, border-color 0.2s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  color: var(--foreground);
}

.title {
  font-size: 24px;
  margin: 0;
  background: linear-gradient(90deg, var(--accent-color), var(--accent-light));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  font-weight: 700;
  font-family: var(--font-geist-mono);
  text-transform: lowercase;
  letter-spacing: 1px;
}

.tagline {
  font-size: 32px;
  text-align: center;
  margin: 0 auto;
  max-width: 700px;
  line-height: 1.3;
}

.highlight {
  font-weight: 600;
  color: var(--accent-color);
}

.tenx {
  font-size: 1.8em;
  font-weight: 700;
  text-decoration: underline;
}

.demoVideo {
  width: 100%;
  display: flex;
  justify-content: center;
  margin: 0 auto;
}

.video {
  max-width: 100%;
  border-radius: 10px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.1);
  width: 100%;
  aspect-ratio: 36 / 30;
  background-color: var(--gray-alpha-100);
}

.features {
  margin-top: 20px;
}

.features h2 {
  font-size: 28px;
  margin-bottom: 16px;
}

.features ul {
  list-style-type: disc;
  padding-left: 20px;
  margin: 0;
  font-size: 16px;
  line-height: 1.6;
}

.features li {
  margin-bottom: 8px;
}

.installation {
  background: var(--gray-alpha-100);
  padding: 24px;
  border-radius: 12px;
  width: 100%;
}

.installation h2 {
  font-size: 28px;
  margin-top: 0;
  margin-bottom: 16px;
}

.codeBlockWrapper {
  position: relative;
  display: flex;
  background: var(--gray-alpha-200);
  border-radius: 8px;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

.codeBlock {
  flex: 1;
  padding: 16px;
  padding-right: 40px;
  overflow-x: auto;
  font-family: var(--font-geist-mono);
  font-size: 14px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  width: 100%;
}

.prerequisites {
  margin-top: 16px;
  margin-bottom: 0;
  font-size: 14px;
  opacity: 0.8;
}

.ctas {
  display: flex;
  gap: 16px;
  margin-top: 0;
  margin-bottom: 32px;
  flex-wrap: wrap;
  justify-content: center;
  width: 100%;
}

.ctas a {
  appearance: none;
  border-radius: 128px;
  height: 48px;
  padding: 0 20px;
  border: none;
  border: 1px solid transparent;
  transition:
    background 0.2s,
    color 0.2s,
    border-color 0.2s;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  line-height: 20px;
  font-weight: 500;
  text-decoration: none;
}

a.primary {
  background: var(--foreground);
  color: var(--background);
  gap: 8px;
}

a.secondary {
  border-color: var(--gray-alpha-200);
  min-width: 158px;
}

.footer {
  text-align: center;
  font-size: 14px;
  color: var(--gray-rgb);
  opacity: 0.7;
  width: 100%;
  padding: 10px 0;
  margin-top: 20px;
}

.footer a {
  color: inherit;
  text-decoration: underline;
}

.copyright {
  margin: 0;
}

/* Enable hover only on non-touch devices */
@media (hover: hover) and (pointer: fine) {
  a.primary:hover {
    background: var(--button-primary-hover);
    border-color: transparent;
  }

  a.secondary:hover {
    background: var(--button-secondary-hover);
    border-color: transparent;
  }

  .footer a:hover {
    text-decoration: underline;
    text-underline-offset: 4px;
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .header {
    padding: 20px 5%;
  }
  
  .main {
    margin: 40px auto;
  }

  .tagline {
    font-size: 26px;
  }

  .features h2, .installation h2 {
    font-size: 24px;
  }
}

@media (max-width: 600px) {
  .header {
    padding: 15px 5%;
    flex-direction: column;
    gap: 15px;
    width: 100%;
    box-sizing: border-box;
  }

  .main {
    align-items: center;
    margin: 30px auto;
    gap: 20px;
    padding: 0 15px;
    width: 100%;
    box-sizing: border-box;
  }

  .tagline {
    font-size: 22px;
    width: 100%;
  }

  .headerActions {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
    gap: 10px;
  }

  .headerButton {
    font-size: 12px;
    height: 32px;
    padding: 0 10px;
    flex: 0 1 auto;
    min-width: 80px;
    text-align: center;
  }
  
  .features ul {
    padding-left: 15px;
  }
  
  .features li {
    margin-bottom: 6px;
  }
  
  .installation {
    padding: 16px;
  }
  
  .codeBlockWrapper {
    display: flex;
    max-width: 100%;
    width: 100%;
    flex-direction: column;
  }

  .codeBlock {
    font-size: 12px;
    padding: 12px;
    width: 100%;
    white-space: pre-wrap;
    word-break: break-all;
    overflow-wrap: break-word;
  }
  
  .ctas {
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .ctas a {
    height: 40px;
    padding: 0 15px;
    font-size: 14px;
    min-width: 120px;
  }
}

@media (prefers-color-scheme: dark) {
  .logo {
    filter: invert();
  }
}
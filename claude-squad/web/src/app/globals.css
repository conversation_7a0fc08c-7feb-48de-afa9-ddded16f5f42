:root {
  --background: #ffffff;
  --foreground: #171717;
  --accent-color: #4a55af;
  --accent-light: #7983d8;
}

@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --background: #0a0a0a;
    --foreground: #ededed;
    --accent-color: #6e79d8;
    --accent-light: #9098e9;
  }
}

html[data-theme="light"] {
  --background: #ffffff;
  --foreground: #171717;
  --accent-color: #4a55af;
  --accent-light: #7983d8;
}

html[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #ededed;
  --accent-color: #6e79d8;
  --accent-light: #9098e9;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  margin-bottom: 1%;
}

a {
  color: inherit;
  text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
  line-height: 1.2;
  margin-top: 0;
}

pre, code {
  font-family: var(--font-geist-mono), monospace;
}

@media (prefers-color-scheme: dark) {
  html:not([data-theme="light"]) {
    color-scheme: dark;
  }
}

html[data-theme="dark"] {
  color-scheme: dark;
}
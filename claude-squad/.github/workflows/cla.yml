name: "CLA Assistant"
on:
  issue_comment:
    types: [created]
  pull_request_target:
    types: [opened,closed,synchronize]

permissions:
  actions: write
  contents: read
  pull-requests: write
  statuses: write

jobs:
  CLAAssistant:
    runs-on: ubuntu-latest
    steps:
      - name: "CLA Assistant"
        if: (github.event.comment.body == 'recheck' || github.event.comment.body == 'I have read the CLA Document and I hereby sign the CLA') || github.event_name == 'pull_request_target'
        uses: contributor-assistant/github-action@v2.6.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          PERSONAL_ACCESS_TOKEN: ${{ secrets.CLA_REPO_SECRET }}
        with:
          path-to-signatures: 'signatures/version1/cla.json'
          path-to-document: 'https://github.com/smtg-ai/claude-squad/blob/main/CLA.md'
          branch: 'main'

          remote-organization-name: 'smtg-ai'
          remote-repository-name: 'claude-squad-clas'

          custom-notsigned-prcomment: 'Thank you for your contribution! Please sign the [CLA](https://github.com/smtg-ai/claude-squad/blob/main/CLA.md) before we can merge your pull request. You can sign the CLA by just posting a comment following the below format.'

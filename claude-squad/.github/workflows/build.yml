name: Build

on:
  push:
    branches: [ main ]
    paths:
      - '**.go'
      - 'go.mod'
      - 'go.sum'
      - '.github/workflows/build.yml'
  pull_request:
    branches: [ main ]
    paths:
      - '**.go'
      - 'go.mod'
      - 'go.sum'
      - '.github/workflows/build.yml'

jobs:
  build:
    name: Build Binary and Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        goos: [linux, darwin, windows]
        goarch: [amd64, arm64]
        exclude:
          - goos: windows
            goarch: arm64

    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.23'
        cache: true

    - name: Run tests
      run: go test -v ./...

    - name: Build
      env:
        GOOS: ${{ matrix.goos }}
        GOARCH: ${{ matrix.goarch }}
      run: |
        BINARY_NAME=claude-squad
        if [ "${{ matrix.goos }}" = "windows" ]; then
          BINARY_NAME=$BINARY_NAME.exe
        fi
        go build -v -o build/${{ matrix.goos }}_${{ matrix.goarch }}/$BINARY_NAME

    - name: Upload artifacts
      uses: actions/upload-artifact@v4
      with:
        name: claude-squad-${{ matrix.goos }}-${{ matrix.goarch }}
        path: build/${{ matrix.goos }}_${{ matrix.goarch }}/*
        retention-days: 7

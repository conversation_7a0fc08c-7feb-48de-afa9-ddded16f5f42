name: Deploy to GitHub Pages

on:
  push:
    branches:
      - main
    paths:
      - 'web/**'
      - '.github/workflows/deploy-pages.yml'
  pull_request:
    branches:
      - main
    paths:
      - 'web/**'
      - '.github/workflows/deploy-pages.yml'

permissions:
  contents: read
  pages: write
  id-token: write

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: web/package-lock.json

      - name: Install dependencies
        run: cd web && npm ci

      - name: Update Next.js config for static export
        run: |
          cd web
          # Add output: 'export' to next.config.ts

      - name: Build website
        run: cd web && npm run build

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./web/out
          
  deploy:
    if: github.event_name == 'push'  # Only deploy on push to main, not on PR
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    steps:
      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
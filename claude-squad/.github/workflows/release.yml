name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    permissions: write-all
    name: Build Release
    runs-on: ubuntu-latest

    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Go 1.23
        uses: actions/setup-go@v4
        with:
          go-version: '1.23'

      - name: <PERSON> GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          HOMEBREW_REPO_TOKEN: ${{ secrets.BREW_TOKEN }}

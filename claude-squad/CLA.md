# Contributor License Agreement

Thank you for your interest in contributing to [Claude Squad](https://github.com/smtg-ai/claude-squad) ("the Project"). This Contributor License Agreement ("CLA") ensures that your Contributions can be used in the Project while allowing you to retain ownership of your work.

By signing this CLA, you accept and agree to the following terms for your present and future Contributions to the Project.

## Definitions

**"You"**: The individual or legal entity submitting Contributions to the Project.

**"Contribution"**: Any original work of authorship, including source code, documentation, configuration files, or other materials, that you intentionally submit to the Project.

**"Project"**: [Claude Squad](https://github.com/smtg-ai/claude-squad).

## 1. Grant of Rights

**Copyright License**: You grant the Project maintainers and recipients a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable copyright license to reproduce, modify, publicly display, sublicense, and distribute your Contributions under any license terms.

**Patent License**: You grant a perpetual, worldwide, non-exclusive, no-charge, royalty-free, irrevocable patent license for any patent claims necessarily infringed by your Contributions. If you initiate patent litigation against the Project, your patent licenses terminate.

## 2. Representations

You represent that:

a) You are legally entitled to grant the above licenses

b) If employed, you have permission from your employer or your employer has waived rights to your Contributions

c) Each Contribution is your original creation

d) You will identify any third-party materials included in your Contributions

## 3. Relicensing Rights

You grant the Project maintainers the right to relicense your Contributions under any OSI-approved open-source license to ensure the Project can adapt its licensing while maintaining its open-source nature.

## 4. Disclaimer

You provide your Contributions "AS IS" without warranties of any kind. You are not required to provide support for your Contributions unless you choose to do so.

## 5. How to Sign

You may accept this CLA through the CLA assistant bot when submitting your first pull request

## 6. Acceptance

By submitting a Contribution to the Project, you agree to be bound by the terms of this CLA. You may indicate your agreement on your PR.

<br />

*Questions? Contact [<EMAIL>](mailto:<EMAIL>) or [<EMAIL>](mailto:<EMAIL>)*

name: 'Build Docker images for Cognee'
description: 'Build cognee-related Docker images and push to the Docker registry (AWS ECR)'
inputs:
  stage:
    description: 'The stage of the pipeline, such as "dev" or "prd", for the Cognee app'
    required: true
  aws_account_id:
    description: 'The AWS account ID for the Cognee app'
    required: true
  should_publish:
    description: 'Whether to publish the Cognee Docker image to AWS ECR; should be either "true" or "false"'
    required: true
  ecr_image_repo_name:
    description: 'The Docker image ECR repository name for the Cognee app, such as "workflows"'
    required: true
  dockerfile_location:
    description: 'The  directory location of the Dockerfile for the Cognee app'
    required: true

runs:
  using: "composite"
  steps:
    - name: Build PromethAI App Docker image
      shell: bash
      env:
        STAGE: ${{ inputs.stage }}
      run: |
        export SHA_SHORT="$(git rev-parse --short HEAD)"
        export CUR_DATE="$(date +%Y%m%d%H%M%S)"
        export VERSION="${{ inputs.stage }}-$CUR_DATE-$SHA_SHORT"
        export STAGE="${{ inputs.stage }}"
        export APP_DIR="$PWD/${{ inputs.dockerfile_location }}"
        image_name="${{ inputs.ecr_image_repo_name }}" version="$VERSION" account="${{ inputs.aws_account_id }}" app_dir="$APP_DIR" publish="${{ inputs.should_publish }}" ./bin/dockerize
        echo "Docker tag is: $VERSION"
        echo $VERSION > /tmp/.DOCKER_IMAGE_VERSION

name: publish dev | Docker image

on:
  push:
    branches:
      - dev
    paths-ignore:
      - '**.md'
      - 'examples/**'

env:
  AWS_ROLE_DEV_CICD: "arn:aws:iam::************:role/cognee-dev-base-role-github-ci-cd"
  AWS_ACCOUNT_ID_DEV: "************"

jobs:

  publish_docker_to_ecr:
    name: Publish Cognee Docker image
    runs-on: ubuntu-22.04
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout code from repo
        uses: actions/checkout@v4

      - name: Set environment variable for stage
        id: set-env
        run: |
          echo "STAGE=dev" >> $GITHUB_ENV
          echo "::set-output name=stage::dev"

      - name: Use output
        run: echo "The stage is ${{ steps.set-env.outputs.stage }}"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE_DEV_CICD }}
          aws-region: eu-west-1

      - name: Build Docker image and push to ECR
        uses: ./.github/actions/image_builder
        id: build-cognee-docker-image
        with:
          stage: dev
          aws_account_id: ${{ env.AWS_ACCOUNT_ID_DEV }}
          should_publish: true
          ecr_image_repo_name: cognee-dev-backend-cognee-ecr
          dockerfile_location: ./

      - name: Export Cognee image tag
        id: export-cognee-image-tag
        run: |
          export IMAGE_TAG=$(cat /tmp/.DOCKER_IMAGE_VERSION)
          echo "Docker tag is: $IMAGE_TAG"
          echo "cognee_image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT

    outputs:
      cognee_image_tag: ${{ steps.export-cognee-image-tag.outputs.cognee_image_tag }}

  trigger_deployment:
    name: Trigger deployment
    runs-on: ubuntu-latest
    needs: publish_docker_to_ecr
    steps:
      - name: Trigger deployment in the infrastructure
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.PAT_FOR_CROSS_REPOS_CICD_TRIGGERING }}
          script: |
            await github.rest.actions.createWorkflowDispatch({
              owner: 'topoteretes',
              repo: 'cognee-infra',
              workflow_id: 'terraform.apply.yml',
              ref: 'main'
            })

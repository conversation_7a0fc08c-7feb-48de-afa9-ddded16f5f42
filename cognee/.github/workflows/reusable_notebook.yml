name: test-notebook

on:
  workflow_call:
    inputs:
      notebook-location:
        description: "Location of Ju<PERSON>ter notebook to run"
        required: true
        type: string
    secrets:
      GRAPHISTRY_USERNAME:
        required: true
      GRAPHISTRY_PASSWORD:
        required: true
      #LLM_MODEL:
      #  required: true
      #LLM_ENDPOINT:
      #  required: true
      LLM_API_KEY:
        required: true
      OPENAI_API_KEY:
        required: true
      #LLM_API_VERSION:
      #  required: true
      EMBEDDING_MODEL:
        required: true
      EMBEDDING_ENDPOINT:
        required: true
      EMBEDDING_API_KEY:
        required: true
      EMBEDDING_API_VERSION:
        required: true

env:
  RUNTIME__LOG_LEVEL: ERROR

jobs:

  run_notebook_test:
    name: test
    runs-on: ubuntu-22.04
    defaults:
      run:
        shell: bash
    steps:
      - name: Check out
        uses: actions/checkout@master

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ inputs.python-version }}

      - name: Install specific db dependency
        run: |
          poetry install -E notebook

      - name: Execute Jupyter Notebook
        env:
          ENV: 'dev'
          #LLM_MODEL: ${{ secrets.LLM_MODEL }}
          #LLM_ENDPOINT: ${{ secrets.LLM_ENDPOINT }}
          LLM_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }} # Use OpenAI Until a multimedia model is deployed and DeepEval support for other models is added
          #LLM_API_VERSION: ${{ secrets.LLM_API_VERSION }}
          EMBEDDING_MODEL: ${{ secrets.EMBEDDING_MODEL }}
          EMBEDDING_ENDPOINT: ${{ secrets.EMBEDDING_ENDPOINT }}
          EMBEDDING_API_KEY: ${{ secrets.EMBEDDING_API_KEY }}
          EMBEDDING_API_VERSION: ${{ secrets.EMBEDDING_API_VERSION }}
          GRAPHISTRY_USERNAME: ${{ secrets.GRAPHISTRY_USERNAME }}
          GRAPHISTRY_PASSWORD: ${{ secrets.GRAPHISTRY_PASSWORD }}
        run: |
          poetry run jupyter nbconvert \
          --to notebook \
          --execute ${{ inputs.notebook-location }} \
          --output executed_notebook.ipynb \
          --ExecutePreprocessor.timeout=1200

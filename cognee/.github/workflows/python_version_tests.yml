name: Reusable Python Version Tests

on:
  workflow_call:
    inputs:
      python-versions:
        required: false
        type: string
        default: '["3.10.x", "3.11.x", "3.12.x"]'
    secrets:
      GRAPHISTRY_USERNAME:
        required: true
      GRAPHISTRY_PASSWORD:
        required: true
      LLM_PROVIDER:
        required: true
      LLM_MODEL:
        required: true
      LLM_ENDPOINT:
        required: true
      LLM_API_KEY:
        required: true
      LLM_API_VERSION:
        required: true
      EMBEDDING_PROVIDER:
        required: true
      EMBEDDING_MODEL:
        required: true
      EMBEDDING_ENDPOINT:
        required: true
      EMBEDDING_API_KEY:
        required: true
      EMBEDDING_API_VERSION:
        required: true

env:
  RUNTIME__LOG_LEVEL: ERROR
  ENV: 'dev'

jobs:
  run-python-version-tests:
    name: Python ${{ matrix.python-version }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        python-version: ${{ fromJSON(inputs.python-versions) }}
        os: [ubuntu-22.04, macos-13, macos-15, windows-latest]
      fail-fast: false
    steps:
      - name: Check out
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ matrix.python-version }}

      - name: Run unit tests
        shell: bash
        run: poetry run pytest cognee/tests/unit/
        env:
          PYTHONUTF8: 1
          LLM_PROVIDER: openai
          LLM_MODEL: ${{ secrets.LLM_MODEL }}
          LLM_ENDPOINT: ${{ secrets.LLM_ENDPOINT }}
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_VERSION: ${{ secrets.LLM_API_VERSION }}

          EMBEDDING_PROVIDER: openai
          EMBEDDING_MODEL: ${{ secrets.EMBEDDING_MODEL }}
          EMBEDDING_ENDPOINT: ${{ secrets.EMBEDDING_ENDPOINT }}
          EMBEDDING_API_KEY: ${{ secrets.EMBEDDING_API_KEY }}
          EMBEDDING_API_VERSION: ${{ secrets.EMBEDDING_API_VERSION }}

      - name: Run integration tests
        if: ${{ !contains(matrix.os, 'windows') }}
        shell: bash
        run: poetry run pytest cognee/tests/integration/
        env:
          PYTHONUTF8: 1
          LLM_PROVIDER: openai
          LLM_MODEL: ${{ secrets.LLM_MODEL }}
          LLM_ENDPOINT: ${{ secrets.LLM_ENDPOINT }}
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_VERSION: ${{ secrets.LLM_API_VERSION }}

          EMBEDDING_PROVIDER: openai
          EMBEDDING_MODEL: ${{ secrets.EMBEDDING_MODEL }}
          EMBEDDING_ENDPOINT: ${{ secrets.EMBEDDING_ENDPOINT }}
          EMBEDDING_API_KEY: ${{ secrets.EMBEDDING_API_KEY }}
          EMBEDDING_API_VERSION: ${{ secrets.EMBEDDING_API_VERSION }}

      - name: Run default basic pipeline
        shell: bash
        env:
          PYTHONUTF8: 1
          GRAPHISTRY_USERNAME: ${{ secrets.GRAPHISTRY_USERNAME }}
          GRAPHISTRY_PASSWORD: ${{ secrets.GRAPHISTRY_PASSWORD }}

          LLM_PROVIDER: openai
          LLM_MODEL: ${{ secrets.LLM_MODEL }}
          LLM_ENDPOINT: ${{ secrets.LLM_ENDPOINT }}
          LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
          LLM_API_VERSION: ${{ secrets.LLM_API_VERSION }}

          EMBEDDING_PROVIDER: openai
          EMBEDDING_MODEL: ${{ secrets.EMBEDDING_MODEL }}
          EMBEDDING_ENDPOINT: ${{ secrets.EMBEDDING_ENDPOINT }}
          EMBEDDING_API_KEY: ${{ secrets.EMBEDDING_API_KEY }}
          EMBEDDING_API_VERSION: ${{ secrets.EMBEDDING_API_VERSION }}
        run: poetry run python ./cognee/tests/test_library.py

      - name: Build with Poetry
        shell: bash
        run: poetry build

      - name: Install Package
        if: ${{ !contains(matrix.os, 'windows') }}
        run: |
          cd dist
          pip install *.whl

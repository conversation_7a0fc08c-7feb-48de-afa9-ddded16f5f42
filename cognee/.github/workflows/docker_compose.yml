name: test | docker compose

on:
  workflow_call:

jobs:
  docker-compose-test:
    runs-on: ubuntu-22.04

    steps:
    - name: Checkout repository
      uses: actions/checkout@master

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker images
      env:
        ENVIRONMENT: dev
        ENV: dev
      run: |
        docker compose -f docker-compose.yml build

    - name: Run Docker Compose
      env:
        ENVIRONMENT: dev
        ENV: dev
      run: |
        docker compose -f docker-compose.yml up -d

    - name: Wait for services to be ready
      run: |
        # Add any necessary health checks or wait commands
        sleep 30

    - name: Shut down Docker Compose
      if: always()
      run: |
        docker compose -f docker-compose.yml down

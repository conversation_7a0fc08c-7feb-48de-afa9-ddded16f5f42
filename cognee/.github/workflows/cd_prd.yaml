name: publish prd | Docker image

on:
  push:
    branches:
        - main
    paths-ignore:
      - '**.md'
      - 'examples/**'

env:
  AWS_ROLE_DEV_CICD: "arn:aws:iam::************:role/cognee-dev-base-role-github-ci-cd"
  AWS_ACCOUNT_ID_DEV: "************"
  ENVIRONMENT: prd

jobs:

  publish_docker_to_ecr:
    name: Publish Docker PromethAI image
    runs-on: ubuntu-22.04
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Checkout code from repo
        uses: actions/checkout@v4

      - name: Set environment variable for stage
        id: set-env
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            echo "STAGE=prd" >> $GITHUB_ENV
            echo "::set-output name=stage::prd"
          else
            echo "STAGE=dev" >> $GITHUB_ENV
            echo "::set-output name=stage::dev"
          fi

      - name: Use output
        run: echo "The stage is ${{ steps.set-env.outputs.stage }}"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ env.AWS_ROLE_DEV_CICD }}
          aws-region: eu-west-1

      - name: Build Docker image and push to ECR
        uses: ./.github/actions/image_builder
        id: generate-promethai-docker
        with:
          stage: prd
          aws_account_id: ${{ env.AWS_ACCOUNT_ID_DEV }}
          should_publish: true
          ecr_image_repo_name: cognee-prd-backend-cognee-ecr
          dockerfile_location: ./

      - name: Export Docker image tag
        id: export-cognee-docker-tag
        run: |
          export DOCKER_TAG=$(cat /tmp/.DOCKER_IMAGE_VERSION)
          echo "Docker tag is: $DOCKER_TAG"
          echo "cognee_image_tag=$DOCKER_TAG" >> $GITHUB_OUTPUT

    #   - name: Create Tag and Release
    #     runs-on: ubuntu-latest
    #     uses: actions/checkout@v3
    #     needs: publish_docker_to_ecr  # ensure this job runs after Docker image is pushed
    #     steps:
    #       - name: Check out code
    #         uses: actions/checkout@v3
    #       - name: Bump version and push tag
    #         id: bump_version_and_push_tag
    #         uses: anothrNick/github-tag-action@1.34.0
    #         env:
    #           GITHUB_TOKEN: ${{ secrets.PAT_FOR_CROSS_REPOS_CICD_TRIGGERING }}
    #           WITH_V: true
    #           DEFAULT_BUMP: 'minor'  # or 'minor' or 'major'
    #       - name: Create Release
    #         id: create_release
    #         uses: actions/create-release@v1
    #         env:
    #           GITHUB_TOKEN: ${{ secrets.PAT_FOR_CROSS_REPOS_CICD_TRIGGERING }}
    #         with:
    #           tag_name: ${{ steps.bump_version_and_push_tag.outputs.tag }}
    #           release_name: Release ${{ steps.bump_version_and_push_tag.outputs.tag }}

    outputs:
      cognee_image_tag: ${{ steps.export-promethai-docker-tag.outputs.cognee_image_tag }}

  trigger_deployment:
    name: Trigger deployment
    runs-on: ubuntu-latest
    needs: publish_docker_to_ecr
    steps:
      - name: TF apply workflow triggers step
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.PAT_FOR_CROSS_REPOS_CICD_TRIGGERING }}
          script: |
            await github.rest.actions.createWorkflowDispatch({
              owner: 'topoteretes',
              repo: 'cognee-infra',
              workflow_id: 'terraform.apply.yml',
              ref: 'main'
            })

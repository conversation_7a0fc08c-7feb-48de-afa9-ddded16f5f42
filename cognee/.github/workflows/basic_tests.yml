name: Reusable Basic Tests

on:
  workflow_call:
    inputs:
      python-version:
        required: false
        type: string
        default: '3.11.x'
    secrets:
      GRAPHISTRY_USERNAME:
        required: true
      GRAPHISTRY_PASSWORD:
        required: true
      LLM_PROVIDER:
        required: true
      LLM_MODEL:
        required: true
      LLM_ENDPOINT:
        required: true
      LLM_API_KEY:
        required: true
      LLM_API_VERSION:
        required: true
      EMBEDDING_PROVIDER:
        required: true
      EMBEDDING_MODEL:
        required: true
      EMBEDDING_ENDPOINT:
        required: true
      EMBEDDING_API_KEY:
        required: true
      EMBEDDING_API_VERSION:
        required: true

env:
  RUNTIME__LOG_LEVEL: ERROR
  ENV: 'dev'

jobs:

  lint:
    name: Run Linting
    runs-on: ubuntu-22.04
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ inputs.python-version }}

      - name: Run Linting
        uses: astral-sh/ruff-action@v2

  format-check:
    name: Run Formatting Check
    runs-on: ubuntu-22.04
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ inputs.python-version }}

      - name: Run Formatting Check
        uses: astral-sh/ruff-action@v2
        with:
          args: "format --check"

  unit-tests:
    name: Run Unit Tests
    runs-on: ubuntu-22.04
    env:
      LLM_PROVIDER: openai
      LLM_MODEL: ${{ secrets.LLM_MODEL }}
      LLM_ENDPOINT: ${{ secrets.LLM_ENDPOINT }}
      LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
      LLM_API_VERSION: ${{ secrets.LLM_API_VERSION }}

      EMBEDDING_PROVIDER: openai
      EMBEDDING_MODEL: ${{ secrets.EMBEDDING_MODEL }}
      EMBEDDING_ENDPOINT: ${{ secrets.EMBEDDING_ENDPOINT }}
      EMBEDDING_API_KEY: ${{ secrets.EMBEDDING_API_KEY }}
      EMBEDDING_API_VERSION: ${{ secrets.EMBEDDING_API_VERSION }}
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ inputs.python-version }}

      - name: Run Unit Tests
        run: poetry run pytest cognee/tests/unit/

  integration-tests:
    name: Run Integration Tests
    runs-on: ubuntu-22.04
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ inputs.python-version }}

      - name: Run Integration Tests
        run: poetry run pytest cognee/tests/integration/

  simple-examples:
    name: Run Simple Examples
    runs-on: ubuntu-22.04
    env:
      GRAPHISTRY_USERNAME: ${{ secrets.GRAPHISTRY_USERNAME }}
      GRAPHISTRY_PASSWORD: ${{ secrets.GRAPHISTRY_PASSWORD }}

      LLM_PROVIDER: openai
      LLM_MODEL: ${{ secrets.LLM_MODEL }}
      LLM_ENDPOINT: ${{ secrets.LLM_ENDPOINT }}
      LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
      LLM_API_VERSION: ${{ secrets.LLM_API_VERSION }}

      EMBEDDING_PROVIDER: openai
      EMBEDDING_MODEL: ${{ secrets.EMBEDDING_MODEL }}
      EMBEDDING_ENDPOINT: ${{ secrets.EMBEDDING_ENDPOINT }}
      EMBEDDING_API_KEY: ${{ secrets.EMBEDDING_API_KEY }}
      EMBEDDING_API_VERSION: ${{ secrets.EMBEDDING_API_VERSION }}
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ inputs.python-version }}

      - name: Run Simple Examples
        run: poetry run python ./examples/python/simple_example.py

  graph-tests:
    name: Run Basic Graph Tests
    runs-on: ubuntu-22.04
    env:
      GRAPHISTRY_USERNAME: ${{ secrets.GRAPHISTRY_USERNAME }}
      GRAPHISTRY_PASSWORD: ${{ secrets.GRAPHISTRY_PASSWORD }}

      LLM_PROVIDER: openai
      LLM_MODEL: ${{ secrets.LLM_MODEL }}
      LLM_ENDPOINT: ${{ secrets.LLM_ENDPOINT }}
      LLM_API_KEY: ${{ secrets.LLM_API_KEY }}
      LLM_API_VERSION: ${{ secrets.LLM_API_VERSION }}

      EMBEDDING_PROVIDER: openai
      EMBEDDING_MODEL: ${{ secrets.EMBEDDING_MODEL }}
      EMBEDDING_ENDPOINT: ${{ secrets.EMBEDDING_ENDPOINT }}
      EMBEDDING_API_KEY: ${{ secrets.EMBEDDING_API_KEY }}
      EMBEDDING_API_VERSION: ${{ secrets.EMBEDDING_API_VERSION }}
    steps:
      - name: Check out repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: ${{ inputs.python-version }}

      - name: Run Graph Tests
        run: poetry run python ./examples/python/code_graph_example.py --repo_path ./cognee/tasks/graph

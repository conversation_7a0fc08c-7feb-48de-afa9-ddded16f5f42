name: Reusable Notebook Tests

on:
  workflow_call:

jobs:
#  run-main-notebook:
#    name: Main Notebook Test
#    uses: ./.github/workflows/reusable_notebook.yml
#    with:
#      notebook-location: notebooks/cognee_demo.ipynb
#    secrets: inherit

  run-cognee-multimedia:
    name: Cognee Multimedia Notebook
    uses: ./.github/workflows/reusable_notebook.yml
    with:
      notebook-location: notebooks/cognee_multimedia_demo.ipynb
    secrets: inherit

name: build test | Docker image

on:
  workflow_call:

env:
  AWS_ACCOUNT_ID_DEV: "************"

jobs:

  build_docker:
    name: Build Cognee Backend Docker App Image
    runs-on: ubuntu-22.04
    steps:
      - name: Check out Cognee code
        uses: actions/checkout@v3

      - name: Build Cognee Docker image
        id: cognee-docker-tag
        run: |
          export SHA_SHORT="$(git rev-parse --short HEAD)"
          export CUR_DATE="$(date +%Y%m%d%H%M%S)"
          export VERSION="dev-$CUR_DATE-$SHA_SHORT"
          image_name="cognee" docker_login="false" version="$VERSION" account="${{ env.AWS_ACCOUNT_ID_DEV }}" app_dir="." publish="false" ./bin/dockerize
          export DOCKER_TAG=$(cat /tmp/.DOCKER_IMAGE_VERSION)
          echo "Successfully built cognee Docker image. Tag is: $DOCKER_TAG"

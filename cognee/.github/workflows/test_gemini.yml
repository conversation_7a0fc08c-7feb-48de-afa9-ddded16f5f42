name: test | gemini

on:
  workflow_call:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: false

jobs:
  test-gemini:
    name: Run Gemini Test
    runs-on: ubuntu-22.04
    steps:
      - name: Check out repository
        uses: actions/checkout@v4

      - name: Cognee Setup
        uses: ./.github/actions/cognee_setup
        with:
          python-version: '3.11.x'

      - name: Run Gemini Simple Example
        env:
          LLM_PROVIDER: "gemini"
          LLM_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          LLM_MODEL: "gemini/gemini-1.5-flash"
          EMBEDDING_PROVIDER: "gemini"
          EMBEDDING_API_KEY: ${{ secrets.GEMINI_API_KEY }}
          EMBEDDING_MODEL: "gemini/text-embedding-004"
          EMBEDDING_DIMENSIONS: "768"
          EMBEDDING_MAX_TOKENS: "8076"
        run: poetry run python ./examples/python/simple_example.py

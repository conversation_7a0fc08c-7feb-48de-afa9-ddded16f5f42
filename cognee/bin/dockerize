set -euo pipefail

AWS_REGION=${region:-eu-west-1}
AWS_DEPLOYMENT_ACCOUNT=${account:-************}
AWS_REPOSITORY=${repo:-"${AWS_DEPLOYMENT_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com"}

STAGE=${stage:-"dev"}
SHA_SHORT="$(git rev-parse --short HEAD)"
CUR_DATE="$(date +%Y%m%d%H%M%S)"
VERSION="$STAGE-$CUR_DATE-$SHA_SHORT"
IMAGE_NAME=${image_name:-cognee-${STAGE}}

REPO_NAME="${AWS_REPOSITORY}/${IMAGE_NAME}"
FULL_IMAGE_NAME="${REPO_NAME}"
APP_DIR=${app_dir:-"."}

# ECHO "$FULL_IMAGE_NAME:latest"

PUBLISH=${publish:-false}

echo "Building docker image ${FULL_IMAGE_NAME} located in dir ${app_dir}"

pushd "${APP_DIR}" &&
  docker buildx build --platform linux/amd64 \
    --build-arg STAGE=${STAGE} \
    -t "${FULL_IMAGE_NAME}:latest" -t "${FULL_IMAGE_NAME}:${VERSION}" . &&
  echo "${VERSION}" >/tmp/.DOCKER_IMAGE_VERSION &&
  echo "Successfully built docker image ${FULL_IMAGE_NAME}"

if [ "${PUBLISH}" = true ]; then
  echo "Pushing docker image ${FULL_IMAGE_NAME} to ECR repository to AWS account ${AWS_DEPLOYMENT_ACCOUNT}"
  if [ "${PUBLISH}" = true ]; then
    echo "logging in"
    aws ecr get-login-password --region "${AWS_REGION}" | docker login --username AWS --password-stdin "${AWS_REPOSITORY}"
  fi
  docker push "${FULL_IMAGE_NAME}:latest" && docker push "${FULL_IMAGE_NAME}:${VERSION}" &&
    echo "Successfully pushed docker image ${FULL_IMAGE_NAME} to ECR repository"
fi

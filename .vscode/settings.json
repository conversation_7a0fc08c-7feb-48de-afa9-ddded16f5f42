{
    "python.analysis.typeCheckingMode": "standard",
    "windsurfPyright.analysis.diagnosticMode": "workspace",
    "windsurfPyright.analysis.typeCheckingMode": "standard",
    "yaml.schemas": {
        "/Users/<USER>/.windsurf/extensions/continue.continue-1.1.47-darwin-arm64/config-yaml-schema.json": [
            ".continue/**/*.yaml"
        ],
        "/Users/<USER>/.windsurf/extensions/continue.continue-1.1.48-darwin-arm64/config-yaml-schema.json": [
            ".continue/**/*.yaml"
        ],
        "/Users/<USER>/.windsurf/extensions/continue.continue-1.1.49-darwin-arm64/config-yaml-schema.json": [
            ".continue/**/*.yaml"
        ],
        "schemaservice:/Users/<USER>/.windsurf/extensions/continue.continue-1.1.49-darwin-arm64/config-yaml-schema.json": "file:///Users/<USER>/.continue/config.yaml"
    },
}